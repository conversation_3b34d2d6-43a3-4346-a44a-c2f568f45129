*.tar
*.tar.gz
*.zip

boeing-demo-deploy/docker-images.tar
boeing-demo-deploy/contents.tar.gz
boeing-demo-deploy/maptiles.tar.gz
CAW-Z-2412-030258-MS.ova


boeing-demo-deploy/contents/
boeing-demo-deploy/maptiles.cloud.immfly.io/
boeing-demo-deploy/docker-images/

__pycache__/
*.pyc
*.pyo
*.pyd
.Python
env/
venv/
.venv/
*.egg-info/
.installed.cfg
*.egg
pip-wheel-metadata/

node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

.vscode/

.DS_Store
Thumbs.db

*.log

/squashfs-complete*

/VMs*
/squashfs\output\010-boeing-app*
/squashfs\build_root*