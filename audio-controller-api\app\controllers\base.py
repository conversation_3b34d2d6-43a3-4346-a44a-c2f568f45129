import time
from abc import ABC, abstractmethod
from typing import Any

from pydantic import BaseModel


class PlayingFile(BaseModel):
    filepath: str
    start_time_ms: int = 0
    started_playing_at: float | None = None
    play_object: Any = None


class BaseAudioController(ABC):
    def __init__(self) -> None:
        print("Init called")
        self.playing_files: dict[str, PlayingFile] = {}

    def play(self, filepath: str, start_time_ms: int = 0, stop: bool = True) -> None:
        """
        Play an audio file.

        Args:
            filepath: Path to the audio file.
            start_time_ms: The time in milliseconds to start playing the file from.
            stop: Whether to stop the file if it is already playing.
        """
        playing_file = self.playing_files.get(filepath)
        if playing_file and stop:
            self.stop(filepath)
        if not playing_file:
            playing_file = PlayingFile(filepath=filepath)
        playing_file.start_time_ms = start_time_ms
        playing_file.started_playing_at = time.time()
        playing_file.play_object = self._perform_play(playing_file, start_time_ms)
        self.playing_files[filepath] = playing_file

    def stop(self, filepath: str) -> None:
        """
        Stop playing an audio file.

        Args:
            filepath: Path to the audio file to stop playing.
        """
        playing_file = self.playing_files.get(filepath)
        if playing_file:
            self._perform_stop(playing_file)
            self.playing_files.pop(filepath)

    def pause(self, filepath: str) -> None:
        """
        Pause playing an audio file.

        Args:
            filepath: Path to the audio file to pause playing.
        """
        print(self.playing_files)
        playing_file = self.playing_files.get(filepath)
        if not playing_file:
            return
        if not playing_file.play_object:
            return
        pause_time = time.time()
        played_time_ms = int((pause_time - (playing_file.started_playing_at or 0)) * 1000)
        resume_overhead = playing_file.start_time_ms + played_time_ms
        playing_file.start_time_ms = resume_overhead
        self._perform_stop(playing_file)

    def resume(self, filepath:str) -> None:
        """
        Resume playing an audio file.

        Args:
            filepath: Path to the audio file to resume playing.
        """
        playing_file = self.playing_files.get(filepath)
        if not playing_file:
            print("No playing file")
            return
        if playing_file.play_object and playing_file.play_object.is_playing():
            return
        print(playing_file, playing_file.play_object.is_playing())  # noqa: T201
        self.play(filepath, playing_file.start_time_ms, stop=False)

    @abstractmethod
    def _perform_play(self, playing_file: PlayingFile, start_time_ms: int = 0) -> Any: # noqa: ANN401
        """
        Play the audio file using the appropriate lower-level system.

        Args:
            playing_file: The PlayingFile object to play.
            start_time_ms: The time in milliseconds to start playing the file from.

        Returns:
            Any: The object used to play the audio file
        """

    @abstractmethod
    def _perform_stop(self, playing_file: PlayingFile) -> None:
        """
        Stop the audio file playback using the appropriate lower-level system.

        Args:
            playing_file: The PlayingFile object to play.
        """
