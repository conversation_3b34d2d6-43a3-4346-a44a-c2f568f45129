from typing import Any
import socket
from config import Settings

from controllers.base import BaseAudioController, PlayingFile
from pydub import AudioSegment


class ArincAudioController(BaseAudioController):
    def __init__(self, host: str, port: int):
        super().__init__()
        self.host = host
        self.port = port
        self.socket = None
        self._is_playing = False

    def _perform_play(self, playing_file: PlayingFile, start_time_ms: int = 0) -> Any:  # noqa: ANN401
        """
        Play an audio file using the ARINC 628 protocol.
        Args:
            playing_file:
            start_time_ms:
        Returns:
        """
        if self._is_playing:
            raise RuntimeError("Already playing an audio file")

        try:
            self._configure_socket()

            audio = AudioSegment.from_file(playing_file.filepath)
            audio = self._check_arinc_specifications(audio)

            header = (f"{audio.channels},"
                      f"{audio.sample_width},"
                      f"{audio.frame_rate}")

            print(f"Audio header: {header}")
            self._send(header.encode())
            print(f"Audio transmission started at {self.host}:{self.port}")
            self._is_playing = True

            start_ms = 0
            while self._is_playing:
                end_ms = start_ms + Settings.CHUNK_SIZE
                audio_chunk = audio[start_ms:end_ms]

                if len(audio_chunk) == 0:
                    break

                chunk_data = audio_chunk.raw_data
                print(f"Sending chunk of size {len(chunk_data)}")

                while len(chunk_data) > Settings.BUFF_SIZE:
                    self._send(chunk_data[:Settings.BUFF_SIZE])
                    chunk_data = chunk_data[Settings.BUFF_SIZE:]

                self._send(chunk_data)
                start_ms = end_ms

            print("Audio transmission complete")
        except Exception as e:
            print(f"Error during transmission: {e}")
            raise
        finally:
            self.stop(playing_file.filepath)

    def _perform_stop(self, playing_file: PlayingFile) -> None:
        """
        Stop playing an audio file using the ARINC protocol.
        Args:
            playing_file:
        Returns:
        """
        self._is_playing = False
        if self.socket:
            self.socket.close()
            self.socket = None
            print("Audio transmission stopped")

    def _configure_socket(self) -> None:
        """
        Configure the socket for the ARINC protocol.
        Returns:
        """
        # Create a UDP socket
        self.socket = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        # Set the socket options
        self.socket.setsockopt(socket.SOL_SOCKET, socket.SO_RCVBUF, Settings.BUFF_SIZE)
        self.socket.setsockopt(socket.IPPROTO_IP, socket.IP_TTL, Settings.ARINC_TTL)

        if Settings.ARINC_BIT_DONT_FRAGMENT:
            self.socket.setsockopt(socket.IPPROTO_IP, 10, 1)

    def _check_arinc_specifications(self, audio: AudioSegment) -> AudioSegment:
        """
        Check that the ARINC specifications are met.
        Returns:
        """
        if audio.frame_rate != Settings.ARINC_FRAME_RATE or \
            audio.sample_width != Settings.ARINC_SAMPLE_RATE or \
            audio.channels != Settings.ARINC_CHANNELS:

            audio = (
                audio.
                set_frame_rate(Settings.ARINC_FRAME_RATE).
                set_sample_width(Settings.ARINC_SAMPLE_RATE).
                set_channels(Settings.ARINC_CHANNELS)
            )

        return audio

    def _send(self, chunk_data: bytes) -> None:
        """
        Send a chunk of audio data.
        Args:
            chunk_data:
        """
        self.socket.sendto(chunk_data, (self.host, self.port))
