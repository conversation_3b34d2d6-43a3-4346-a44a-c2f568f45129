from typing import Any

from pydub import AudioSegment
from pydub.playback import _play_with_simpleaudio

from controllers.base import BaseAudioController, PlayingFile


class AlsaAudioController(BaseAudioController):
    def _perform_play(self, playing_file: PlayingFile, start_time_ms: int = 0) -> Any: # noqa: ANN401
        audio = AudioSegment.from_file(playing_file.filepath)
        audio = audio[start_time_ms:]
        return _play_with_simpleaudio(audio)

    def _perform_stop(self, playing_file: PlayingFile) -> None:
        playing_file.play_object.stop()
